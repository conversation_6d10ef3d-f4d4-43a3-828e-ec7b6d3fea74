<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testVideoAPI()">Test Video API</button>
    <div id="result"></div>

    <script>
        async function testVideoAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';

            try {
                console.log('Testing API endpoint: /api/videos-simple.php');
                const response = await fetch('/api/videos-simple.php?page=1&limit=1');
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success && data.data && data.data.videos && data.data.videos.length > 0) {
                    const video = data.data.videos[0];
                    resultDiv.innerHTML = `
                        <h3>✅ API Response Success:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <h3>Video URL Test:</h3>
                        <video controls width="400">
                            <source src="${video.video_url}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <h3>Thumbnail Test:</h3>
                        <img src="${video.thumbnail_url}" width="200" alt="Thumbnail">
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ API Response Failed:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
