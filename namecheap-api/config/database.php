<?php
class Database {
    private static $instance = null;
    private $connection;

    private function __construct() {
        try {
            // MySQL connection for cPanel hosting
            $host = 'localhost';
            $dbname = 'bluerpcm_bluefilmx';  // Update with your actual database name
            $username = 'bluerpcm_dbuser';   // Update with your actual username
            $password = 'kingpatrick100';    // Update with your actual password
            
            $this->connection = new PDO(
                "mysql:host={$host};dbname={$dbname};charset=utf8mb4",
                $username,
                $password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->connection;
    }

    public static function setCORSHeaders() {
        $allowed_origins = [
            'https://bluefilmx.com',
            'https://www.bluefilmx.com',
            'http://localhost:3000',
            'http://127.0.0.1:3000'
        ];
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        if (in_array($origin, $allowed_origins)) {
            header('Access-Control-Allow-Origin: ' . $origin);
        }
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        header('Access-Control-Allow-Credentials: true');
    }
}
?>