# Allow access to media files
Options +Indexes
DirectoryIndex disabled

# Enable CORS for media files
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</IfModule>

# Set proper MIME types for video files
<IfModule mod_mime.c>
    AddType video/mp4 .mp4
    AddType video/webm .webm
    AddType video/ogg .ogv
    AddType video/quicktime .mov
    AddType video/x-msvideo .avi
    AddType video/x-matroska .mkv
</IfModule>

# Set proper MIME types for image files
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
</IfModule>

# Enable compression for media files
<IfModule mod_deflate.c>
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|webp)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar|mp4|webm|mov|avi|mkv)$ no-gzip dont-vary
</IfModule>

# Cache control for media files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType video/webm "access plus 1 month"
    ExpiresByType video/quicktime "access plus 1 month"
    ExpiresByType video/x-msvideo "access plus 1 month"
    ExpiresByType video/x-matroska "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>
