/**
 * Enhanced Authentication Store
 * Provides comprehensive user authentication with improved security and UX
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { apiClient } from '../lib/api';

export interface User {
  id: string;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  bio?: string;
  is_active: boolean;
  is_verified: boolean;
  is_approved: boolean;
  email_verified_at?: string;
  last_login_at?: string;
  profile_visibility: 'public' | 'private' | 'friends';
  allow_messages: boolean;
  created_at: string;
  updated_at: string;
}

export interface AuthSession {
  token: string;
  sessionId: string;
  expiresAt: number;
  refreshToken?: string;
}

interface AuthState {
  // State
  user: User | null;
  session: AuthSession | null;
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
  
  // Computed
  isAuthenticated: boolean;
  isVerified: boolean;
  isApproved: boolean;
  needsVerification: boolean;
  
  // Actions
  signUp: (userData: SignUpData) => Promise<void>;
  signIn: (identifier: string, password: string, rememberMe?: boolean) => Promise<void>;
  signOut: () => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  refreshToken: () => Promise<boolean>;
  loadUser: () => Promise<void>;
  clearError: () => void;
  deleteAccount: () => Promise<void>;
}

export interface SignUpData {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
}

const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes
const AUTO_REFRESH_INTERVAL = 60 * 1000; // 1 minute

export const useAuthStore = create<AuthState>()(n  persist(
    (set, get) => ({
      // Initial state
      user: null,
      session: null,
      isLoading: false,
      isInitialized: false,
      error: null,
      
      // Computed properties
      get isAuthenticated() {
        const { user, session } = get();
        return !!(user && session && session.expiresAt > Date.now());
      },
      
      get isVerified() {
        const { user } = get();
        return user?.is_verified ?? false;
      },
      
      get isApproved() {
        const { user } = get();
        return user?.is_approved ?? false;
      },
      
      get needsVerification() {
        const { user } = get();
        return !!(user && !user.is_verified);
      },
      
      // Actions
      signUp: async (userData: SignUpData) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.register(userData);
          
          if (response.success && response.data) {
            set({
              user: response.data.user,
              isLoading: false,
              error: null
            });
          } else {
            throw new Error(response.error || 'Registration failed');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      signIn: async (identifier: string, password: string, rememberMe = false) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.login(identifier, password);
          
          if (response.success && response.data) {
            const { user, token, session_id, expires_in } = response.data;
            
            const session: AuthSession = {
              token,
              sessionId: session_id,
              expiresAt: Date.now() + (expires_in * 1000)
            };
            
            set({
              user,
              session,
              isLoading: false,
              error: null
            });
            
            // Set up auto token refresh
            get().setupTokenRefresh();
            
          } else {
            throw new Error(response.error || 'Login failed');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      signOut: async () => {
        const { session } = get();
        
        try {
          if (session) {
            await apiClient.logout(session.sessionId);
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            session: null,
            isLoading: false,
            error: null
          });
          
          // Clear any stored data
          localStorage.removeItem('auth-storage');
          
          // Clear auto refresh
          get().clearTokenRefresh();
        }
      },
      
      verifyEmail: async (token: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.verifyEmail(token);
          
          if (response.success) {
            // Reload user data to get updated verification status
            await get().loadUser();
          } else {
            throw new Error(response.error || 'Email verification failed');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Email verification failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      requestPasswordReset: async (email: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.requestPasswordReset(email);
          
          if (!response.success) {
            throw new Error(response.error || 'Password reset request failed');
          }
          
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password reset request failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      resetPassword: async (token: string, password: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.resetPassword(token, password);
          
          if (!response.success) {
            throw new Error(response.error || 'Password reset failed');
          }
          
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      updateProfile: async (data: Partial<User>) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.updateProfile(data);
          
          if (response.success) {
            // Reload user data
            await get().loadUser();
          } else {
            throw new Error(response.error || 'Profile update failed');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      changePassword: async (currentPassword: string, newPassword: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.changePassword(currentPassword, newPassword);
          
          if (!response.success) {
            throw new Error(response.error || 'Password change failed');
          }
          
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password change failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      refreshToken: async (): Promise<boolean> => {
        const { session } = get();
        
        if (!session) {
          return false;
        }
        
        try {
          const response = await apiClient.refreshToken(session.token);
          
          if (response.success && response.data) {
            const newSession: AuthSession = {
              ...session,
              token: response.data.token,
              expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
            };
            
            set({ session: newSession });
            return true;
          }
          
          return false;
        } catch (error) {
          console.error('Token refresh failed:', error);
          return false;
        }
      },
      
      loadUser: async () => {
        const { session } = get();
        
        if (!session || session.expiresAt <= Date.now()) {
          set({ isInitialized: true });
          return;
        }
        
        set({ isLoading: true });
        
        try {
          const response = await apiClient.getCurrentUser();
          
          if (response.success && response.data) {
            set({
              user: response.data.user,
              isLoading: false,
              isInitialized: true,
              error: null
            });
            
            // Set up auto token refresh
            get().setupTokenRefresh();
          } else {
            // Invalid session, clear it
            set({
              user: null,
              session: null,
              isLoading: false,
              isInitialized: true
            });
          }
        } catch (error) {
          console.error('Load user error:', error);
          set({
            user: null,
            session: null,
            isLoading: false,
            isInitialized: true
          });
        }
      },
      
      clearError: () => {
        set({ error: null });
      },
      
      deleteAccount: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await apiClient.deleteAccount();
          
          if (response.success) {
            await get().signOut();
          } else {
            throw new Error(response.error || 'Account deletion failed');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Account deletion failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },
      
      // Token refresh management
      tokenRefreshInterval: null as NodeJS.Timeout | null,
      
      setupTokenRefresh: () => {
        const { tokenRefreshInterval } = get();
        
        // Clear existing interval
        if (tokenRefreshInterval) {
          clearInterval(tokenRefreshInterval);
        }
        
        // Set up new interval
        const interval = setInterval(async () => {
          const { session } = get();
          
          if (!session) {
            get().clearTokenRefresh();
            return;
          }
          
          // Check if token needs refresh (within threshold of expiry)
          const timeUntilExpiry = session.expiresAt - Date.now();
          if (timeUntilExpiry <= TOKEN_REFRESH_THRESHOLD) {
            const success = await get().refreshToken();
            if (!success) {
              // Refresh failed, sign out
              await get().signOut();
            }
          }
        }, AUTO_REFRESH_INTERVAL);
        
        set({ tokenRefreshInterval: interval });
      },
      
      clearTokenRefresh: () => {
        const { tokenRefreshInterval } = get();
        if (tokenRefreshInterval) {
          clearInterval(tokenRefreshInterval);
          set({ tokenRefreshInterval: null });
        }
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        session: state.session
      })
    }
  )
);

// Initialize auth store on app load
// Temporarily disabled until database is configured
// if (typeof window !== 'undefined') {
//   useAuthStore.getState().loadUser();
// }