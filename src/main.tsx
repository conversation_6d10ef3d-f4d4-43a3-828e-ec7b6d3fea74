import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { SWRConfig } from 'swr';
import App from './App.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';
import { setupGlobalErrorHandlers } from './utils/errorHandling';
// import './utils/testSupabase'; // Disabled - using file-based API
import './index.css';

// Setup global error handlers to handle cache and service worker errors gracefully
setupGlobalErrorHandlers();

// Get the root element
const rootElement = document.getElementById('root');

// Make sure the root element exists
if (!rootElement) {
  throw new Error('Root element not found');
}

// SWR Configuration
const swrConfig = {
  errorRetryCount: 3,
  errorRetryInterval: 1000,
  dedupingInterval: 5000,
  focusThrottleInterval: 10000,
  revalidateOnFocus: false,
  revalidateIfStale: true,
  revalidateOnReconnect: true,
  onError: (error: any) => {
    console.error('🚨 Global SWR Error:', error);
  },
  onSuccess: (data: any, key: string) => {
    console.log('✅ Global SWR Success:', key, { dataType: typeof data });
  }
};

// Create the root and render the app
const root = createRoot(rootElement);
root.render(
  <StrictMode>
    <ErrorBoundary>
      <SWRConfig value={swrConfig}>
        <App />
      </SWRConfig>
    </ErrorBoundary>
  </StrictMode>
);
