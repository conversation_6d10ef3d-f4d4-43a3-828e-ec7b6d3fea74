import React, { useState, useEffect, useRef, memo } from 'react';
import { getPlaceholderImage } from '../../utils/imageUtils';
import { getOptimizedImageUrl, fixProductionUrls, getBestImageFormat, generateLQIP } from '../../utils/mediaUtils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  fallbackSrc?: string;
  className?: string;
  width?: number;
  height?: number;
  sizes?: string;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  blur?: boolean;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  fetchPriority?: 'high' | 'low' | 'auto';
  decoding?: 'sync' | 'async' | 'auto';
  progressive?: boolean;
  enableModernFormats?: boolean;
}

/**
 * OptimizedImage component with lazy loading, responsive sizing, and error handling
 * Enhanced with IntersectionObserver for better lazy loading
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  fallbackSrc,
  className = '',
  width = 640,
  height = 360,
  sizes = '100vw',
  priority = false,
  onLoad,
  onError,
  blur = false,
  objectFit = 'cover',
  fetchPriority = 'auto',
  decoding = 'async',
  progressive = true,
  enableModernFormats = true,
}) => {
  // Use our utility function to generate a fallback image if none is provided
  const defaultFallback = getPlaceholderImage(width, height, 'No Image');

  // Get the best image format for the browser
  const bestFormat = enableModernFormats ? getBestImageFormat() : 'auto';

  // Fix and optimize the source URL
  const optimizedSrc = getOptimizedImageUrl(fixProductionUrls(src), width, bestFormat);

  // Generate low-quality placeholder for progressive loading
  const lqipSrc = progressive ? generateLQIP(fixProductionUrls(src)) : null;

  const [imgSrc, setImgSrc] = useState<string>(priority ? optimizedSrc : (lqipSrc || ''));
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isInView, setIsInView] = useState<boolean>(priority);
  const [lqipLoaded, setLqipLoaded] = useState<boolean>(false);
  const [highQualityLoaded, setHighQualityLoaded] = useState<boolean>(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (!priority && imgRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (entry.isIntersecting) {
            setIsInView(true);

            // Progressive loading: start with LQIP if available
            if (progressive && lqipSrc && !lqipLoaded) {
              setImgSrc(lqipSrc);
              setLqipLoaded(true);

              // Preload high-quality image
              const highQualityImg = new Image();
              highQualityImg.onload = () => {
                setImgSrc(optimizedSrc);
                setHighQualityLoaded(true);
              };
              highQualityImg.src = optimizedSrc;
            } else {
              setImgSrc(optimizedSrc);
            }

            // Disconnect after image is in view
            if (observerRef.current) {
              observerRef.current.disconnect();
              observerRef.current = null;
            }
          }
        },
        {
          rootMargin: '100px', // Reduced margin for better performance
          threshold: 0.01,
        }
      );

      observerRef.current.observe(imgRef.current);
    } else if (priority) {
      // For priority images, load immediately
      setImgSrc(optimizedSrc);
      setHighQualityLoaded(true);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, [priority, optimizedSrc, progressive, lqipSrc, lqipLoaded]);

  // Reset state when src changes
  useEffect(() => {
    if (priority || isInView) {
      setImgSrc(optimizedSrc);
      setIsLoaded(false);
    }
  }, [optimizedSrc, priority, isInView]);

  // Handle image load event
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // Handle image error event
  const handleError = () => {
    console.warn('Image failed to load:', {
      currentSrc: imgSrc,
      originalSrc: src,
      optimizedSrc,
      fallbackSrc,
      defaultFallback
    });

    const actualFallback = fallbackSrc || defaultFallback;
    if (imgSrc !== actualFallback) {
      console.log('Trying fallback image:', actualFallback);
      setImgSrc(actualFallback);
    } else {
      console.error('All image sources failed to load, using placeholder');
    }
    if (onError) onError();
  };

  // Generate srcSet for responsive images if width is provided
  const generateSrcSet = () => {
    if (!width || !src || src.startsWith('data:') || src.startsWith('blob:')) {
      return undefined;
    }

    // For Supabase storage URLs, we can add width parameters
    if (src.includes('supabase.co') && src.includes('storage/v1')) {
      try {
        const baseUrl = src.split('?')[0];
        const widths = [
          Math.round(width * 0.5),
          width,
          Math.round(width * 1.5),
          Math.round(width * 2)
        ];

        return widths
          .map(w => `${baseUrl}?width=${w} ${w}w`)
          .join(',');
      } catch (error) {
        console.error('Error generating srcSet:', error);
        return undefined;
      }
    }

    return undefined;
  };

  // Enhanced blur style for progressive loading
  const blurStyle = blur || (progressive && !highQualityLoaded) ? {
    filter: (!isLoaded || (progressive && !highQualityLoaded)) ? 'blur(10px)' : 'none',
    transition: 'filter 0.4s ease-out, opacity 0.3s ease-in-out',
  } : {
    transition: 'opacity 0.3s ease-in-out',
  };

  return (
    <img
      ref={imgRef}
      src={imgSrc || (priority ? optimizedSrc : (fallbackSrc || defaultFallback))}
      alt={alt}
      className={`${className} ${!isLoaded && !priority ? 'opacity-50' : 'opacity-100'}`}
      loading={priority ? 'eager' : 'lazy'}
      fetchPriority={priority ? 'high' : fetchPriority}
      decoding={decoding}
      onLoad={handleLoad}
      onError={handleError}
      width={width}
      height={height}
      sizes={sizes}
      srcSet={(priority || isInView) ? generateSrcSet() : undefined}
      style={{
        ...blurStyle,
        objectFit,
      }}
    />
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(OptimizedImage);
