/**
 * Error handling utilities for the application
 */

/**
 * Handle service worker and cache-related errors gracefully
 */
export const handleCacheError = (error: Error): void => {
  // Log cache errors but don't let them break the app
  if (error.message.includes('Cache') || error.message.includes('service-worker')) {
    console.warn('Cache operation failed (non-critical):', error.message);
    return;
  }

  // Re-throw other errors
  throw error;
};

/**
 * Global error handler for unhandled promise rejections
 */
export const setupGlobalErrorHandlers = (): void => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;

    // Handle cache-related and DOM manipulation errors gracefully
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = String(error.message);
      if (errorMessage.includes('Cache') ||
          errorMessage.includes('service-worker') ||
          errorMessage.includes('Failed to execute \'put\' on \'Cache\'') ||
          errorMessage.includes('removeChild') ||
          errorMessage.includes('not a child of this node') ||
          errorMessage.includes('Failed to execute \'removeChild\' on \'Node\'')) {
        // console.warn('Cache/DOM operation failed (handled):', errorMessage);
        event.preventDefault(); // Prevent the error from being logged as unhandled
        return;
      }
    }

    // Log other unhandled rejections
    console.error('Unhandled promise rejection:', error);
  });

  // Handle general errors
  window.addEventListener('error', (event) => {
    const error = event.error;

    // Handle cache-related and DOM manipulation errors gracefully
    if (error && error.message && (
        error.message.includes('Cache') ||
        error.message.includes('service-worker') ||
        error.message.includes('Failed to execute \'put\' on \'Cache\'') ||
        error.message.includes('removeChild') ||
        error.message.includes('not a child of this node') ||
        error.message.includes('Failed to execute \'removeChild\' on \'Node\'')
    )) {
      // console.warn('Cache/DOM operation failed (handled):', error.message);
      event.preventDefault(); // Prevent the error from being logged
      return;
    }

    // Log other errors
    console.error('Global error:', error);
  });
};

/**
 * Safe fetch wrapper that handles network errors gracefully
 */
export const safeFetch = async (url: string, options?: RequestInit): Promise<Response | null> => {
  try {
    const response = await fetch(url, options);
    return response;
  } catch (error) {
    // console.warn('Fetch failed:', url, error);
    return null;
  }
};

/**
 * Safe JSON parse that returns null on error
 */
export const safeJsonParse = <T>(jsonString: string): T | null => {
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    // console.warn('JSON parse failed:', error);
    return null;
  }
};
