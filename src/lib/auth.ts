/**
 * BetterAuth Configuration
 * Configured for Supabase PostgreSQL database
 */

import { betterAuth } from "better-auth";

// Get Supabase connection details from environment
const getSupabaseConnectionUrl = () => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || process.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    console.warn('VITE_SUPABASE_URL not found, using fallback database configuration');
    return process.env.DATABASE_URL || "postgresql://postgres:password@localhost:5432/bluefilmx_db";
  }

  // Extract database connection from Supabase URL
  const url = new URL(supabaseUrl);
  const projectRef = url.hostname.split('.')[0];

  // Use Supabase direct database connection
  return process.env.DATABASE_URL || `postgresql://postgres:[YOUR_DB_PASSWORD]@db.${projectRef}.supabase.co:5432/postgres`;
};

export const auth = betterAuth({
  database: {
    provider: "postgresql",
    url: getSupabaseConnectionUrl()
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    minPasswordLength: 6,
    maxPasswordLength: 128
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5 // 5 minutes
    }
  },
  user: {
    additionalFields: {
      firstName: {
        type: "string",
        required: false
      },
      lastName: {
        type: "string",
        required: false
      },
      avatarUrl: {
        type: "string",
        required: false
      },
      bio: {
        type: "string",
        required: false
      },
      isActive: {
        type: "boolean",
        defaultValue: true
      },
      isVerified: {
        type: "boolean",
        defaultValue: false
      },
      isApproved: {
        type: "boolean",
        defaultValue: true
      },
      profileVisibility: {
        type: "string",
        defaultValue: "public"
      },
      allowMessages: {
        type: "boolean",
        defaultValue: true
      }
    }
  },
  plugins: [],
  trustedOrigins: [
    "http://localhost:3000",
    "http://localhost:5173",
    "https://bluefilmx.com",
    "https://www.bluefilmx.com"
  ],
  secret: process.env.BETTER_AUTH_SECRET || import.meta.env.VITE_BETTER_AUTH_SECRET || "your-secret-key-change-in-production",
  baseURL: process.env.BETTER_AUTH_URL || import.meta.env.VITE_BETTER_AUTH_URL || "https://bluefilmx.com"
});

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.User;